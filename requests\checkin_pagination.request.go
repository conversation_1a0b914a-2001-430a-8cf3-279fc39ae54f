package requests

import (
	"fmt"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CheckinPaginationRequest struct {
	core.BaseValidator
	StartDate *string `json:"start_date" query:"start_date"`
	EndDate   *string `json:"end_date" query:"end_date"`
	TeamCode  *string `json:"team_code" query:"team_code"`
	UserID    *string `json:"user_id" query:"user_id"`
	Type      *string `json:"type" query:"type"`
	LeaveType *string `json:"leave_type" query:"leave_type"`
	IsUnused  *bool   `json:"is_unused" query:"is_unused"`
}

func (r *CheckinPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.Is<PERSON>ate(r.EndDate, "end_date"))

	if r.TeamCode != nil {
		for i, teamCode := range strings.Split(utils.ToNonPointer(r.TeamCode), ",") {
			fieldName := fmt.Sprintf("team_code[%d]", i)
			teamCodeNoSpace := strings.TrimSpace(teamCode)
			r.Must(r.IsExists(ctx, utils.ToPointer(teamCodeNoSpace), models.Team{}.TableName(), "code", fieldName))
		}
	}

	// Validate user_id exists if provided
	if r.UserID != nil {
		r.Must(r.IsExists(ctx, r.UserID, models.User{}.TableName(), "id", "user_id"))
	}

	// Validate type if provided
	if r.Type != nil {
		validTypes := []string{
			string(models.CheckinTypeOfficeHQ),
			string(models.CheckinTypeWfh),
			string(models.CheckinTypeOnsite),
			string(models.CheckinTypeOfficeAKV),
			string(models.CheckinTypeBusinessTrip),
			string(models.CheckinTypeLeave),
		}
		r.Must(r.IsStrIn(r.Type, strings.Join(validTypes, "|"), "type"))
	}
	if r.LeaveType != nil {
		validTypes := []string{
			string(models.CheckinLeaveTypeAnnual),
			string(models.CheckinLeaveTypeSick),
			string(models.CheckinLeaveTypeBusiness),
			string(models.CheckinLeaveTypeMenstrual),
			string(models.CheckinTypeBusinessTrip),
			string(models.CheckinLeaveTypeBirthday),
			string(models.CheckinLeaveTypeOrdination),
		}
		r.Must(r.IsStrIn(r.Type, strings.Join(validTypes, "|"), "leave_type"))
	}

	return r.Error()
}
