enum PMOProjectStatus {
  DRAFT
  TOR
  BIDDING
  PMO
  WARRANTY
  CLOSED
  CANCEL
}

model pmo_projects {
  id         String           @id @default(uuid()) @db.Uuid
  name       String           @unique
  slug       String           @unique
  email      String
  tags       String[]         @default([])
  status     PMOProjectStatus @default(DRAFT)
  project_id String           @db.Uuid

  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([name, slug])
}
