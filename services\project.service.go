package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IProjectService interface {
	Create(input *ProjectCreatePayload) (*models.Project, core.IError)
	Update(id string, input *ProjectUpdatePayload) (*models.Project, core.IError)
	Find(id string) (*models.Project, core.IError)
	FindByCode(code string) (*models.Project, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
	Delete(id string) core.IError
}

type projectService struct {
	ctx core.IContext
}

func (s projectService) Create(input *ProjectCreatePayload) (*models.Project, core.IError) {
	project := &models.Project{
		BaseModel:   models.NewBaseModel(),
		Name:        input.Name,
		Code:        input.Code,
		Description: utils.ToPointer(input.Description),
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repo.Project(s.ctx).Create(project)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(project.ID)
}

func (s projectService) Update(id string, input *ProjectUpdatePayload) (*models.Project, core.IError) {
	project, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		project.Name = input.Name
	}
	if input.Code != "" {
		project.Code = input.Code
	}
	if input.Description != "" {
		project.Description = utils.ToPointer(input.Description)
	}

	project.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repo.Project(s.ctx).Where("id = ?", id).Updates(project)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(project.ID)
}

func (s projectService) Find(id string) (*models.Project, core.IError) {
	return repo.Project(s.ctx).FindOne("id = ?", id)
}

func (s projectService) FindByCode(code string) (*models.Project, core.IError) {
	return repo.Project(s.ctx).FindOne("code = ?", code)
}

func (s projectService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	return repo.Project(s.ctx, repo.ProjectOrderBy(pageOptions), repo.ProjectWithSearch(pageOptions.Q)).Pagination(pageOptions)
}

func (s projectService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Project(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewProjectService(ctx core.IContext) IProjectService {
	return &projectService{ctx: ctx}
}
