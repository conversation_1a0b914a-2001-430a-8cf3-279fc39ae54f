enum FileAppKey {
  PMO
  CLOCKIN
  TIMESHEET
  COMMON
}

model files {
  id       String     @id @default(uuid()) @db.Uuid
  name     String
  path     String     @unique
  url      String     @unique
  size     Int
  type     String
  checksum String
  app      FileAppKey @default(COMMON)

  created_at    DateTime @default(now())
  created_by_id String?  @db.Uuid
  updated_at    DateTime @default(now()) @updatedAt

  user users? @relation(fields: [created_by_id], references: [id], onDelete: SetNull)

  @@index([name, path, url])
}
