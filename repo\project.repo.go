package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var Project = repository.Make[models.Project]()

func ProjectOrderBy(pageOptions *core.PageOptions) repository.Option[models.Project] {
	return func(c repository.IRepository[models.Project]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func ProjectWithSearch(q string) repository.Option[models.Project] {
	return func(c repository.IRepository[models.Project]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ?", searchTerm)
	}
}
